<?= $this->extend('layouts/dashboard') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="mb-4">
    <div class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-sm-between">
        <div>
            <h2 class="h3 fw-bold text-dark mb-1">Enquiries Management</h2>
            <p class="text-muted">Manage customer enquiries and messages</p>
        </div>
        <div class="mt-3 mt-sm-0">
            <div class="btn-group">
                <button class="btn btn-outline-primary" onclick="markAllAsRead()">
                    <i class="fas fa-check-double me-2"></i>
                    Mark All Read
                </button>
                <button class="btn btn-primary" onclick="exportEnquiries()">
                    <i class="fas fa-download me-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card card-stats border-primary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="icon-stat bg-primary-light">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <p class="text-muted mb-1 small fw-medium">Total Enquiries</p>
                        <p class="h3 fw-bold mb-0"><?= number_format($totalContacts ?? 0) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card card-stats border-warning">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="icon-stat bg-warning-light">
                            <i class="fas fa-bell"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <p class="text-muted mb-1 small fw-medium">Unread</p>
                        <p class="h3 fw-bold mb-0"><?= number_format($unreadContacts ?? 0) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card card-stats border-success">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="icon-stat bg-success-light">
                            <i class="fas fa-reply"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <p class="text-muted mb-1 small fw-medium">This Week</p>
                        <p class="h3 fw-bold mb-0"><?= number_format($weeklyContacts ?? 0) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label fw-medium">Search</label>
                <input type="text" 
                       data-table-search="enquiries-table"
                       placeholder="Search enquiries..." 
                       class="form-control">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-medium">Status</label>
                <select class="form-select">
                    <option value="">All Status</option>
                    <option value="unread">Unread</option>
                    <option value="read">Read</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-medium">Property Type</label>
                <select class="form-select">
                    <option value="">All Types</option>
                    <option value="house">House</option>
                    <option value="apartment">Apartment</option>
                    <option value="villa">Villa</option>
                    <option value="land">Land</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-2"></i>
                    Apply Filters
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Enquiries Table -->
<div class="card">
    <div class="card-header border-bottom">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h5 class="card-title mb-1">Customer Enquiries</h5>
                <p class="card-text text-muted small mb-0">All customer enquiries and messages</p>
            </div>
            <div class="text-muted small" data-table-info="enquiries-table">
                Showing 1-10 of <?= count($contacts ?? []) ?> results
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table id="enquiries-table" data-table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="sortable" data-sort="0">
                            Customer
                            <i class="fas fa-sort sort-indicator ms-1"></i>
                        </th>
                        <th class="sortable" data-sort="1">
                            Property Interest
                            <i class="fas fa-sort sort-indicator ms-1"></i>
                        </th>
                        <th class="sortable" data-sort="2">
                            Message
                            <i class="fas fa-sort sort-indicator ms-1"></i>
                        </th>
                        <th class="sortable" data-sort="3">
                            Date
                            <i class="fas fa-sort sort-indicator ms-1"></i>
                        </th>
                        <th class="sortable" data-sort="4">
                            Status
                            <i class="fas fa-sort sort-indicator ms-1"></i>
                        </th>
                        <th class="text-end">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($contacts)): ?>
                        <?php foreach ($contacts as $contact): ?>
                            <tr class="<?= !$contact['is_read'] ? 'table-warning' : '' ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 2.5rem; height: 2.5rem;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        </div>
                                        <div class="ms-3">
                                            <div class="fw-medium"><?= esc($contact['name']) ?></div>
                                            <div class="text-muted small"><?= esc($contact['email']) ?></div>
                                            <div class="text-muted small"><?= esc($contact['phone']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?= esc(ucfirst($contact['properties_in'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="text-truncate-2" style="max-width: 200px;">
                                        <?= esc(substr($contact['message'], 0, 100)) ?><?= strlen($contact['message']) > 100 ? '...' : '' ?>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M j, Y g:i A', strtotime($contact['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <?php if ($contact['is_read']): ?>
                                        <span class="badge status-read">
                                            <i class="fas fa-check-circle me-1"></i>
                                            Read
                                        </span>
                                    <?php else: ?>
                                        <span class="badge status-unread">
                                            <i class="fas fa-bell me-1"></i>
                                            Unread
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center justify-content-end">
                                        <div class="btn-group btn-group-sm">
                                            <button data-enquiry-quick-view data-enquiry-id="<?= $contact['id'] ?>" 
                                                    class="btn btn-ghost"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if (!$contact['is_read']): ?>
                                                <button onclick="markAsRead(<?= $contact['id'] ?>)" 
                                                        class="btn btn-ghost"
                                                        title="Mark as Read">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button onclick="replyToEnquiry(<?= $contact['id'] ?>)" 
                                                    class="btn btn-ghost"
                                                    title="Reply">
                                                <i class="fas fa-reply"></i>
                                            </button>
                                            <button onclick="deleteEnquiry(<?= $contact['id'] ?>)" 
                                                    class="btn btn-ghost text-danger"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-envelope text-muted mb-3" style="font-size: 4rem;"></i>
                                    <h5 class="text-dark mb-2">No enquiries found</h5>
                                    <p class="text-muted">Customer enquiries will appear here when they contact you.</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Enquiries page functionality
function markAsRead(enquiryId) {
    fetch(`<?= base_url('dashboard/enquiries/mark-read/') ?>${enquiryId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Enquiry marked as read', 'success');
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error marking enquiry as read', 'danger');
    });
}

function markAllAsRead() {
    // Create confirmation modal
    const modalHtml = `
        <div class="modal fade" id="markAllReadModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Mark All as Read</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to mark all enquiries as read?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="confirmMarkAllRead()">
                            Mark All Read
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('markAllReadModal'));
    modal.show();

    document.getElementById('markAllReadModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function confirmMarkAllRead() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('markAllReadModal'));
    modal.hide();

    fetch(`<?= base_url('dashboard/enquiries/mark-all-read') ?>`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('All enquiries marked as read', 'success');
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error marking all enquiries as read', 'danger');
    });
}

function replyToEnquiry(enquiryId) {
    const modalHtml = `
        <div class="modal fade" id="replyEnquiryModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Reply to Enquiry</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="reply-form">
                            <div class="mb-3">
                                <label class="form-label fw-medium">Subject</label>
                                <input type="text" name="subject" class="form-control" placeholder="Re: Your Property Enquiry" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-medium">Message</label>
                                <textarea name="message" rows="6" class="form-control" placeholder="Thank you for your enquiry..." required></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="sendReply(${enquiryId})">
                            <i class="fas fa-paper-plane me-2"></i>Send Reply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('replyEnquiryModal'));
    modal.show();

    document.getElementById('replyEnquiryModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function sendReply(enquiryId) {
    const form = document.getElementById('reply-form');
    const formData = new FormData(form);
    
    showNotification('Sending reply...', 'info');
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('replyEnquiryModal'));
    modal.hide();
    
    // Simulate sending reply
    setTimeout(() => {
        showNotification('Reply sent successfully', 'success');
    }, 1000);
}

function deleteEnquiry(enquiryId) {
    const modalHtml = `
        <div class="modal fade" id="deleteEnquiryModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Delete Enquiry</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center py-3">
                            <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 4rem; height: 4rem;">
                                <i class="fas fa-exclamation-triangle text-danger fs-4"></i>
                            </div>
                            <h6 class="mb-2">Are you sure you want to delete this enquiry?</h6>
                            <p class="text-muted small mb-0">This action cannot be undone.</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" onclick="confirmDeleteEnquiry(${enquiryId})">
                            <i class="fas fa-trash me-2"></i>Delete Enquiry
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('deleteEnquiryModal'));
    modal.show();

    document.getElementById('deleteEnquiryModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function confirmDeleteEnquiry(enquiryId) {
    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteEnquiryModal'));
    modal.hide();

    fetch(`<?= base_url('dashboard/enquiries/delete/') ?>${enquiryId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Enquiry deleted successfully', 'success');
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error deleting enquiry', 'danger');
    });
}

function exportEnquiries() {
    showNotification('Exporting enquiries...', 'info');
    window.location.href = '<?= base_url('dashboard/enquiries/export') ?>';
}
</script>
<?= $this->endSection() ?>
