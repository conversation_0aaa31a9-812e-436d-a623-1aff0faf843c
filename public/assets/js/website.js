// Website JavaScript functionality with Bootstrap 5

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeNavbar();
    initializeAnimations();
    initializeToasts();
    initializePropertyCards();
    initializeForms();
    
    console.log('Website loaded successfully');
});

/**
 * Initialize navbar functionality
 */
function initializeNavbar() {
    const navbar = document.querySelector('.navbar');
    
    // Add scroll effect to navbar
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Close mobile menu when clicking on links
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
}

/**
 * Initialize scroll animations
 */
function initializeAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.card, .property-card, .hero-content');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

/**
 * Initialize toast notifications
 */
function initializeToasts() {
    // Auto-hide toasts after 5 seconds
    const toasts = document.querySelectorAll('.toast.show');
    toasts.forEach(toast => {
        setTimeout(() => {
            const bsToast = bootstrap.Toast.getOrCreateInstance(toast);
            bsToast.hide();
        }, 5000);
    });
}

/**
 * Initialize property card interactions
 */
function initializePropertyCards() {
    const propertyCards = document.querySelectorAll('.property-card, .card');
    
    propertyCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // Add click handler for property cards
        const viewButton = card.querySelector('[data-property-view]');
        if (viewButton) {
            viewButton.addEventListener('click', function(e) {
                e.preventDefault();
                const propertyId = this.getAttribute('data-property-id');
                showPropertyModal(propertyId);
            });
        }
    });
}

/**
 * Initialize form enhancements
 */
function initializeForms() {
    // Add floating label effect
    const formControls = document.querySelectorAll('.form-control, .form-select');
    
    formControls.forEach(control => {
        // Add focus/blur effects
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        control.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // Check if already has value
        if (control.value) {
            control.parentElement.classList.add('focused');
        }
    });
    
    // Handle contact form submission
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleContactFormSubmission(this);
        });
    }
    
    // Handle newsletter subscription
    const newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleNewsletterSubmission(this);
        });
    }
}

/**
 * Show property modal
 */
function showPropertyModal(propertyId) {
    const modalHtml = `
        <div class="modal fade" id="propertyModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Property Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 250px;">
                                    <span class="text-muted">Property Image</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-semibold mb-3">Property Information</h6>
                                <div class="mb-2">
                                    <strong>Type:</strong> <span class="text-muted">House</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Location:</strong> <span class="text-muted">Dublin, Ireland</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Area:</strong> <span class="text-muted">2,500 sq ft</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Bedrooms:</strong> <span class="text-muted">4</span>
                                </div>
                                <div class="mb-2">
                                    <strong>Bathrooms:</strong> <span class="text-muted">3</span>
                                </div>
                                <div class="mb-3">
                                    <strong>Price:</strong> <span class="text-primary fw-bold">€450,000</span>
                                </div>
                                <p class="text-muted">Beautiful family home in a quiet neighborhood with modern amenities and excellent transport links.</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="contactAboutProperty(${propertyId})">
                            <i class="fas fa-envelope me-2"></i>Contact Agent
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('propertyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('propertyModal'));
    modal.show();

    // Clean up when modal is hidden
    document.getElementById('propertyModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

/**
 * Handle contact form submission
 */
function handleContactFormSubmission(form) {
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    submitButton.disabled = true;
    
    // Simulate form submission (replace with actual AJAX call)
    setTimeout(() => {
        showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
        form.reset();
        
        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    }, 2000);
}

/**
 * Handle newsletter subscription
 */
function handleNewsletterSubmission(form) {
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Subscribing...';
    submitButton.disabled = true;
    
    // Simulate subscription (replace with actual AJAX call)
    setTimeout(() => {
        showNotification('Successfully subscribed to newsletter!', 'success');
        form.reset();
        
        // Reset button
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    }, 1500);
}

/**
 * Contact about property
 */
function contactAboutProperty(propertyId) {
    // Hide property modal
    const propertyModal = bootstrap.Modal.getInstance(document.getElementById('propertyModal'));
    propertyModal.hide();
    
    // Show contact form or redirect
    showNotification('Redirecting to contact form...', 'info');
    
    setTimeout(() => {
        window.location.href = `/contact?property=${propertyId}`;
    }, 1000);
}

/**
 * Show notification toast
 */
function showNotification(message, type = 'info') {
    const toastHtml = `
        <div class="toast" role="alert">
            <div class="toast-header bg-${type} text-white">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">${message}</div>
        </div>
    `;

    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        toastContainer.style.marginTop = '80px';
        document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    const newToast = toastContainer.lastElementChild;
    const bsToast = new bootstrap.Toast(newToast);
    bsToast.show();

    // Remove toast element after it's hidden
    newToast.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

/**
 * Smooth scroll to element
 */
function scrollToElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

/**
 * Initialize property search functionality
 */
function initializePropertySearch() {
    const searchForm = document.getElementById('propertySearchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const searchParams = new URLSearchParams();
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    searchParams.append(key, value);
                }
            }
            
            // Redirect to properties page with search parameters
            window.location.href = `/properties?${searchParams.toString()}`;
        });
    }
}

// Initialize property search when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePropertySearch);

// Make functions globally available
window.showPropertyModal = showPropertyModal;
window.showNotification = showNotification;
window.scrollToElement = scrollToElement;
