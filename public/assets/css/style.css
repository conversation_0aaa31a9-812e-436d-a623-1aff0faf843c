/* Custom CSS for Real Estate Dashboard with Bootstrap 5 */

/* Root Variables for Consistent Theming */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  --light-color: #f8fafc;
  --dark-color: #1e293b;
  --border-color: #e2e8f0;
  --text-muted: #64748b;
  --sidebar-width: 256px;
}

/* Custom Primary Colors */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.text-primary {
  color: var(--primary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

/* Modern Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  min-height: 100vh;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  overflow-y: auto;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.show {
  transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-logo {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.sidebar-logo i {
  color: white;
  font-size: 1.25rem;
}

.sidebar-brand h6 {
  color: white;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.sidebar-brand small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
}

/* User Profile Section */
.sidebar-user {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.user-avatar i {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.user-info {
  flex: 1;
}

.user-name {
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.125rem;
}

.user-role {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
}

/* Navigation Sections */
.nav-section {
  margin-bottom: 2rem;
  padding: 0 1.25rem;
}

.nav-section-title {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.75rem;
  padding-left: 0.5rem;
}

.nav-section-bottom {
  position: absolute;
  bottom: 1rem;
  left: 0;
  right: 0;
  margin-bottom: 0;
}

/* Navigation Links */
.sidebar-nav .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin-bottom: 0.25rem;
  border-radius: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  font-weight: 500;
}

.sidebar-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(4px);
}

.sidebar-nav .nav-link.active {
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  color: white;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.sidebar-nav .nav-link.active::before {
  content: '';
  position: absolute;
  left: -1.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 2rem;
  background: white;
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  border-radius: 0.375rem;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.sidebar-nav .nav-link:hover .nav-icon {
  background: rgba(255, 255, 255, 0.2);
}

.sidebar-nav .nav-link.active .nav-icon {
  background: rgba(255, 255, 255, 0.2);
}

.nav-text {
  flex: 1;
  font-size: 0.875rem;
}

.nav-badge {
  background: var(--danger-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  min-width: 1.5rem;
  text-align: center;
}

.nav-badge.bg-danger {
  background: #ef4444 !important;
}

/* Logout Link Special Styling */
.nav-link-logout {
  color: rgba(239, 68, 68, 0.8) !important;
}

.nav-link-logout:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
}

.nav-link-logout .nav-icon {
  background: rgba(239, 68, 68, 0.1);
}

.nav-link-logout:hover .nav-icon {
  background: rgba(239, 68, 68, 0.2);
}

/* Main Content Area */
.main-content {
  margin-left: 0;
  transition: margin-left 0.3s ease-in-out;
  min-height: 100vh;
  background: #f8fafc;
}

@media (min-width: 992px) {
  .sidebar {
    transform: translateX(0);
  }

  .main-content {
    margin-left: var(--sidebar-width);
  }
}

/* Header Styles */
.header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  height: 4.5rem;
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header h1 {
  color: var(--dark-color);
  font-weight: 700;
  font-size: 1.5rem;
  margin: 0;
}

/* Mobile Sidebar Toggle */
#sidebarToggle {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  background: rgba(37, 99, 235, 0.1);
  border: none;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

#sidebarToggle:hover {
  background: rgba(37, 99, 235, 0.2);
  transform: scale(1.05);
}

/* Modern Card Enhancements */
.card {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: white;
  overflow: hidden;
}

.card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}

.card-title {
  color: var(--dark-color);
  font-weight: 700;
  font-size: 1.125rem;
  margin-bottom: 0.25rem;
}

.card-text {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.card-body {
  padding: 1.5rem;
}

/* Stats Cards */
.card-stats {
  position: relative;
  overflow: hidden;
}

.card-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-color), #3b82f6);
}

.card-stats.border-primary::before {
  background: linear-gradient(180deg, var(--primary-color), #3b82f6);
}

.card-stats.border-success::before {
  background: linear-gradient(180deg, var(--success-color), #059669);
}

.card-stats.border-warning::before {
  background: linear-gradient(180deg, var(--warning-color), #d97706);
}

.card-stats.border-info::before {
  background: linear-gradient(180deg, var(--info-color), #2563eb);
}

/* Modern Table Enhancements */
.table {
  border-collapse: separate;
  border-spacing: 0;
}

.table thead th {
  border-top: none;
  border-bottom: 2px solid #e2e8f0;
  font-weight: 700;
  color: var(--dark-color);
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 1rem 1.5rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table tbody td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.table-hover tbody tr {
  transition: all 0.2s ease;
}

.table-hover tbody tr:hover {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.02) 0%, rgba(59, 130, 246, 0.02) 100%);
  transform: scale(1.001);
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  position: relative;
}

.table th.sortable:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #f1f5f9 100%);
  color: var(--primary-color);
}

.sort-indicator {
  margin-left: 0.5rem;
  opacity: 0.4;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.sort-indicator.active {
  opacity: 1;
  color: var(--primary-color);
}

/* Table Responsive Wrapper */
.table-responsive {
  border-radius: 0.75rem;
  overflow: hidden;
}

/* Table Row Highlighting */
.table-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.05) 100%) !important;
}

.table-warning:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(251, 191, 36, 0.1) 100%) !important;
}

/* Badge Styles */
.badge {
  font-weight: 500;
}

.badge-property-house {
  background-color: var(--primary-color);
  color: white;
}

.badge-property-apartment {
  background-color: var(--success-color);
  color: white;
}

.badge-property-villa {
  background-color: var(--warning-color);
  color: white;
}

.badge-property-land {
  background-color: var(--info-color);
  color: white;
}

/* Modern Button Enhancements */
.btn {
  font-weight: 600;
  border-radius: 0.5rem;
  padding: 0.625rem 1.25rem;
  transition: all 0.2s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-ghost {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
  color: var(--text-muted);
  border-radius: 0.375rem;
  padding: 0.5rem;
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-ghost:hover {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary-color);
  transform: scale(1.05);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
}

.btn-group .btn-ghost {
  margin: 0 0.125rem;
}

/* Mobile-First Responsive Design */

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Mobile Optimizations */
@media (max-width: 575.98px) {
  .sidebar {
    width: 100%;
    max-width: 280px;
  }

  .card-body {
    padding: 1rem;
  }

  .table thead th,
  .table tbody td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .header h1 {
    font-size: 1.25rem;
  }

  .nav-section {
    padding: 0 1rem;
  }

  .sidebar-header {
    padding: 1rem;
  }

  .sidebar-user {
    padding: 1rem;
  }
}

/* Tablet Optimizations */
@media (min-width: 576px) and (max-width: 991.98px) {
  .sidebar {
    width: var(--sidebar-width);
  }

  .main-content {
    margin-left: 0;
  }

  .card-body {
    padding: 1.25rem;
  }
}

/* Desktop Optimizations */
@media (min-width: 992px) {
  .sidebar {
    transform: translateX(0);
    position: fixed;
  }

  .main-content {
    margin-left: var(--sidebar-width);
  }

  .sidebar-overlay {
    display: none;
  }

  #sidebarToggle {
    display: none;
  }
}

/* Large Desktop Optimizations */
@media (min-width: 1400px) {
  .container-fluid {
    max-width: 1320px;
    margin: 0 auto;
  }

  .card {
    border-radius: 1.25rem;
  }

  .table thead th,
  .table tbody td {
    padding: 1.25rem 1.75rem;
  }
}

/* Touch-Friendly Enhancements */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .nav-link {
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .table tbody tr {
    min-height: 60px;
  }

  .btn-ghost {
    min-width: 44px;
    min-height: 44px;
  }
}

/* Print Styles */
@media print {
  .sidebar,
  .header,
  .btn,
  .dropdown {
    display: none !important;
  }

  .main-content {
    margin-left: 0 !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid var(--dark-color);
  }

  .btn {
    border: 2px solid currentColor;
  }

  .sidebar {
    border-right: 3px solid var(--primary-color);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Property Image Styles */
.property-image {
  width: 3rem;
  height: 3rem;
  object-fit: cover;
  border-radius: 0.5rem;
}

/* Icon Styles */
.icon-stat {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-stat.bg-primary-light {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.icon-stat.bg-success-light {
  background-color: #d1fae5;
  color: var(--success-color);
}

.icon-stat.bg-warning-light {
  background-color: #fef3c7;
  color: var(--warning-color);
}

.icon-stat.bg-info-light {
  background-color: #dbeafe;
  color: var(--info-color);
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Status Indicators */
.status-unread {
  background-color: #fef3c7;
  color: #92400e;
}

.status-read {
  background-color: #d1fae5;
  color: #065f46;
}

/* Text Utilities */
.text-muted {
  color: var(--text-muted) !important;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
